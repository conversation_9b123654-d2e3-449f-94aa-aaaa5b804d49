import prisma from '@/config/prismaClient';
import { UserType } from '@prisma/client';

export interface FcmTokenData {
  userId: string;
  userType: UserType;
  token: string;
  deviceId?: string;
  platform?: string;
}

/**
 * Register or update FCM token for a user
 */
export const registerFcmToken = async (data: FcmTokenData) => {
  try {
    const { userId, userType, token, deviceId, platform } = data;

    // Check if token already exists
    const existingToken = await prisma.fcmToken.findUnique({
      where: { token }
    });

    if (existingToken) {
      // Update existing token
      const updatedToken = await prisma.fcmToken.update({
        where: { token },
        data: {
          userId,
          userType,
          deviceId,
          platform,
          isActive: true,
          updatedAt: new Date()
        }
      });
      
      console.log(`✅ FCM token updated for ${userType} ${userId}`);
      return updatedToken;
    } else {
      // Create new token
      const newToken = await prisma.fcmToken.create({
        data: {
          userId,
          userType,
          token,
          deviceId,
          platform,
          isActive: true
        }
      });
      
      console.log(`✅ FCM token registered for ${userType} ${userId}`);
      return newToken;
    }
  } catch (error) {
    console.error('❌ Error registering FCM token:', error);
    throw error;
  }
};

/**
 * Get all active FCM tokens for a user
 */
export const getUserFcmTokens = async (userId: string, userType: UserType) => {
  try {
    const tokens = await prisma.fcmToken.findMany({
      where: {
        userId,
        userType,
        isActive: true
      },
      select: {
        token: true,
        platform: true,
        deviceId: true
      }
    });

    return tokens;
  } catch (error) {
    console.error('❌ Error fetching user FCM tokens:', error);
    throw error;
  }
};

/**
 * Get all active FCM tokens for multiple users
 */
export const getMultipleUsersFcmTokens = async (userIds: string[], userType: UserType) => {
  try {
    const tokens = await prisma.fcmToken.findMany({
      where: {
        userId: { in: userIds },
        userType,
        isActive: true
      },
      select: {
        token: true,
        userId: true,
        platform: true,
        deviceId: true
      }
    });

    return tokens;
  } catch (error) {
    console.error('❌ Error fetching multiple users FCM tokens:', error);
    throw error;
  }
};

/**
 * Deactivate FCM token
 */
export const deactivateFcmToken = async (token: string) => {
  try {
    const updatedToken = await prisma.fcmToken.update({
      where: { token },
      data: { isActive: false }
    });

    console.log(`✅ FCM token deactivated: ${token}`);
    return updatedToken;
  } catch (error) {
    console.error('❌ Error deactivating FCM token:', error);
    throw error;
  }
};

/**
 * Remove FCM token completely
 */
export const removeFcmToken = async (token: string) => {
  try {
    await prisma.fcmToken.delete({
      where: { token }
    });

    console.log(`✅ FCM token removed: ${token}`);
    return true;
  } catch (error) {
    console.error('❌ Error removing FCM token:', error);
    throw error;
  }
};

/**
 * Clean up invalid/expired tokens
 */
export const cleanupInvalidTokens = async (invalidTokens: string[]) => {
  try {
    if (invalidTokens.length === 0) return;

    await prisma.fcmToken.updateMany({
      where: {
        token: { in: invalidTokens }
      },
      data: { isActive: false }
    });

    console.log(`✅ Cleaned up ${invalidTokens.length} invalid FCM tokens`);
  } catch (error) {
    console.error('❌ Error cleaning up invalid tokens:', error);
    throw error;
  }
};
