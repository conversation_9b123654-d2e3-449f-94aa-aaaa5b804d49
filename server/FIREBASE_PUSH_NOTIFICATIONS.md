# Firebase Push Notifications Implementation

## Overview
This document describes the Firebase Cloud Messaging (FCM) push notification system implemented in the UEST application server.

## Setup Instructions

### 1. Firebase Project Configuration

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select existing project
3. Navigate to Project Settings > Service Accounts
4. Generate a new private key (JSON file)
5. Extract the following values from the JSON file:
   - `project_id`
   - `private_key`
   - `client_email`

### 2. Environment Variables

Add the following environment variables to your `.env` file:

```env
# Firebase Configuration
FIREBASE_PROJECT_ID="your-project-id"
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"
FIREBASE_CLIENT_EMAIL="<EMAIL>"
```

**Important:** Make sure to replace `\n` with actual newlines in the private key, or use double quotes and keep the `\n` as literal characters.

### 3. Database Schema

The FCM token management uses the `FcmToken` model with the following structure:

```prisma
model FcmToken {
  id        String   @id @default(uuid())
  userId    String
  userType  UserType
  token     String   @unique
  deviceId  String?
  platform  String?  // 'android', 'ios', 'web'
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId, userType])
  @@index([token])
  @@index([isActive])
}
```

## API Endpoints

### FCM Token Management

#### Register FCM Token
- **POST** `/api/v1/fcm-tokens/register` (Classes)
- **POST** `/api/v1/fcm-tokens/student/register` (Students)
- **POST** `/api/v1/fcm-tokens/admin/register` (Admin)

**Request Body:**
```json
{
  "token": "fcm-token-string",
  "deviceId": "device-unique-id",
  "platform": "android|ios|web"
}
```

#### Get User's FCM Tokens
- **GET** `/api/v1/fcm-tokens/my-tokens`

#### Deactivate FCM Token
- **POST** `/api/v1/fcm-tokens/deactivate`

**Request Body:**
```json
{
  "token": "fcm-token-string"
}
```

#### Remove FCM Token
- **DELETE** `/api/v1/fcm-tokens/remove`

**Request Body:**
```json
{
  "token": "fcm-token-string"
}
```

### Test Endpoints

#### Test Push to Self
- **POST** `/api/v1/push-notifications/test-self` (Classes)
- **POST** `/api/v1/push-notifications/test-student-self` (Students)
- **POST** `/api/v1/push-notifications/test-admin-self` (Admin)

**Request Body:**
```json
{
  "title": "Test Notification",
  "message": "This is a test push notification",
  "data": {
    "key1": "value1",
    "key2": "value2"
  },
  "imageUrl": "https://example.com/image.jpg",
  "clickAction": "OPEN_APP"
}
```

#### Test Push to Specific User (Admin Only)
- **POST** `/api/v1/push-notifications/test-to-user`

**Request Body:**
```json
{
  "userId": "user-uuid",
  "userType": "STUDENT|CLASS|ADMIN",
  "title": "Test Notification",
  "message": "This is a test push notification",
  "data": {
    "key1": "value1"
  }
}
```

#### Test Push to Multiple Users (Admin Only)
- **POST** `/api/v1/push-notifications/test-to-multiple-users`

**Request Body:**
```json
{
  "userIds": ["user-uuid-1", "user-uuid-2"],
  "userType": "STUDENT|CLASS|ADMIN",
  "title": "Test Notification",
  "message": "This is a test push notification"
}
```

#### Test Push to All Admins (Admin Only)
- **POST** `/api/v1/push-notifications/test-to-all-admins`

**Request Body:**
```json
{
  "title": "Admin Notification",
  "message": "This is a notification for all admins"
}
```

## Integration with Existing Notification System

The push notification system is automatically integrated with the existing notification system. When you create notifications using:

- `createNotification()` - Automatically sends push notification to the specific user
- `createAdminNotification()` - Automatically sends push notification to all admin users

## Services and Functions

### FCM Token Service (`/services/fcmTokenService.ts`)

- `registerFcmToken()` - Register or update FCM token
- `getUserFcmTokens()` - Get active tokens for a user
- `getMultipleUsersFcmTokens()` - Get tokens for multiple users
- `deactivateFcmToken()` - Deactivate a token
- `removeFcmToken()` - Remove a token completely
- `cleanupInvalidTokens()` - Clean up invalid/expired tokens

### Push Notification Service (`/services/pushNotificationService.ts`)

- `sendPushNotificationToUser()` - Send to single user
- `sendPushNotificationToMultipleUsers()` - Send to multiple users
- `sendPushNotificationToAllAdmins()` - Send to all admins

## Error Handling

The system includes comprehensive error handling:

- Invalid/expired tokens are automatically cleaned up
- Failed deliveries are logged
- Firebase configuration errors are handled gracefully
- If Firebase is not configured, notifications are skipped with warnings

## Testing

1. **Setup Firebase**: Configure your Firebase project and add environment variables
2. **Register FCM Token**: Use the registration endpoints to add FCM tokens
3. **Test Notifications**: Use the test endpoints to verify push notifications work
4. **Integration Test**: Create regular notifications and verify push notifications are sent

## React Native Integration

For React Native integration, you'll need to:

1. Install `@react-native-firebase/messaging`
2. Configure Firebase in your React Native app
3. Get FCM token and register it using the API endpoints
4. Handle incoming notifications in your app

Example React Native code:
```javascript
import messaging from '@react-native-firebase/messaging';

// Get FCM token
const fcmToken = await messaging().getToken();

// Register token with your server
await fetch('/api/v1/fcm-tokens/student/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    token: fcmToken,
    platform: Platform.OS
  })
});
```

## Security Considerations

- FCM tokens are stored securely in the database
- Only authenticated users can register tokens
- Admin-only endpoints are protected with admin authentication
- Invalid tokens are automatically cleaned up
- Environment variables should be kept secure

## Monitoring and Logs

The system provides comprehensive logging:
- Token registration/updates
- Push notification delivery status
- Failed deliveries and cleanup operations
- Firebase configuration status

Check server logs for push notification related messages prefixed with:
- ✅ (Success operations)
- ❌ (Error operations)
- ⚠️ (Warning operations)
- ℹ️ (Info operations)
