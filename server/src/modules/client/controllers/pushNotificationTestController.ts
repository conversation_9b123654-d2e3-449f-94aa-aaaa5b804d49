import { Request, Response } from 'express';
import { UserType } from '@prisma/client';
import {
  sendPushNotificationToUser,
  sendPushNotificationToMultipleUsers,
  sendPushNotificationToAllAdmins
} from '@/services/pushNotificationService';
import { sendSuccess, sendError } from '@/utils/response';

/**
 * Test endpoint to send push notification to a specific user
 */
export const testSendPushToUserController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, userType, title, message, data, imageUrl, clickAction } = req.body;

    if (!userId || !userType || !title || !message) {
      sendError(res, 'Missing required fields: userId, userType, title, message', 400);
      return;
    }

    const result = await sendPushNotificationToUser({
      userId,
      userType: userType as UserType,
      title,
      message,
      data,
      imageUrl,
      clickAction
    });

    sendSuccess(res, result, 'Push notification test completed');
  } catch (error) {
    console.error('Error in test push notification:', error);
    sendError(res, 'Failed to send test push notification', 500);
  }
};

/**
 * Test endpoint to send push notification to multiple users
 */
export const testSendPushToMultipleUsersController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userIds, userType, title, message, data, imageUrl, clickAction } = req.body;

    if (!userIds || !Array.isArray(userIds) || !userType || !title || !message) {
      sendError(res, 'Missing required fields: userIds (array), userType, title, message', 400);
      return;
    }

    const result = await sendPushNotificationToMultipleUsers({
      userIds,
      userType: userType as UserType,
      title,
      message,
      data,
      imageUrl,
      clickAction
    });

    sendSuccess(res, result, 'Push notification test completed');
  } catch (error) {
    console.error('Error in test push notification to multiple users:', error);
    sendError(res, 'Failed to send test push notification', 500);
  }
};

/**
 * Test endpoint to send push notification to all admins
 */
export const testSendPushToAllAdminsController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { title, message, data, imageUrl, clickAction } = req.body;

    if (!title || !message) {
      sendError(res, 'Missing required fields: title, message', 400);
      return;
    }

    const result = await sendPushNotificationToAllAdmins({
      title,
      message,
      data,
      imageUrl,
      clickAction
    });

    sendSuccess(res, result, 'Push notification test to all admins completed');
  } catch (error) {
    console.error('Error in test push notification to all admins:', error);
    sendError(res, 'Failed to send test push notification', 500);
  }
};

/**
 * Test endpoint to send push notification to current authenticated user
 */
export const testSendPushToSelfController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { title, message, data, imageUrl, clickAction } = req.body;
    const { id: userId, userType } = req.user as any;

    if (!title || !message) {
      sendError(res, 'Missing required fields: title, message', 400);
      return;
    }

    const result = await sendPushNotificationToUser({
      userId,
      userType,
      title,
      message,
      data,
      imageUrl,
      clickAction
    });

    sendSuccess(res, result, 'Push notification test to self completed');
  } catch (error) {
    console.error('Error in test push notification to self:', error);
    sendError(res, 'Failed to send test push notification', 500);
  }
};

/**
 * Test endpoint for student to send push notification to self
 */
export const testSendPushToStudentSelfController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { title, message, data, imageUrl, clickAction } = req.body;
    const { id: userId } = req.student as any;

    if (!title || !message) {
      sendError(res, 'Missing required fields: title, message', 400);
      return;
    }

    const result = await sendPushNotificationToUser({
      userId,
      userType: UserType.STUDENT,
      title,
      message,
      data,
      imageUrl,
      clickAction
    });

    sendSuccess(res, result, 'Push notification test to student self completed');
  } catch (error) {
    console.error('Error in test push notification to student self:', error);
    sendError(res, 'Failed to send test push notification', 500);
  }
};

/**
 * Test endpoint for admin to send push notification to self
 */
export const testSendPushToAdminSelfController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { title, message, data, imageUrl, clickAction } = req.body;
    const { id: userId } = req.admin as any;

    if (!title || !message) {
      sendError(res, 'Missing required fields: title, message', 400);
      return;
    }

    const result = await sendPushNotificationToUser({
      userId,
      userType: UserType.ADMIN,
      title,
      message,
      data,
      imageUrl,
      clickAction
    });

    sendSuccess(res, result, 'Push notification test to admin self completed');
  } catch (error) {
    console.error('Error in test push notification to admin self:', error);
    sendError(res, 'Failed to send test push notification', 500);
  }
};
