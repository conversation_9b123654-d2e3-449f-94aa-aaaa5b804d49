import express from 'express';
import {
  testSendPushToUserController,
  testSendPushToMultipleUsersController,
  testSendPushToAllAdminsController,
  testSendPushToSelfController,
  testSendPushToStudentSelfController,
  testSendPushToAdminSelfController
} from '../controllers/pushNotificationTestController';
import { authClientMiddleware } from '@/middlewares/clientAuth';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import { authMiddleware } from '@/middlewares/adminAuth';

const router = express.Router();

// Test routes for Classes (using authClientMiddleware)
router.post('/test-self', authClientMiddleware, testSendPushToSelfController);

// Test routes for Students (using studentAuthMiddleware)
router.post('/test-student-self', studentAuthMiddleware, testSendPushToStudentSelfController);

// Test routes for Admin (using authMiddleware)
router.post('/test-admin-self', authMiddleware, testSendPushToAdminSelfController);
router.post('/test-to-user', authMiddleware, testSendPushToUserController);
router.post('/test-to-multiple-users', authMiddleware, testSendPushToMultipleUsersController);
router.post('/test-to-all-admins', authMiddleware, testSendPushToAllAdminsController);

export default router;
