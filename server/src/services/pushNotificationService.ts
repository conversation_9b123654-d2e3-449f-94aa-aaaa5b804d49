import { messaging } from '@/config/firebase';
import { UserType } from '@prisma/client';
import { getUserFcmTokens, getMultipleUsersFcmTokens, cleanupInvalidTokens } from './fcmTokenService';

export interface PushNotificationData {
  title: string;
  message: string;
  data?: Record<string, string>;
  imageUrl?: string;
  clickAction?: string;
}

export interface SingleUserNotification extends PushNotificationData {
  userId: string;
  userType: UserType;
}

export interface MultipleUsersNotification extends PushNotificationData {
  userIds: string[];
  userType: UserType;
}

/**
 * Send push notification to a single user
 */
export const sendPushNotificationToUser = async (notification: SingleUserNotification) => {
  try {
    if (!messaging) {
      console.warn('⚠️ Firebase messaging not initialized. Skipping push notification.');
      return { success: false, error: 'Firebase not configured' };
    }

    const { userId, userType, title, message, data, imageUrl, clickAction } = notification;

    // Get user's FCM tokens
    const userTokens = await getUserFcmTokens(userId, userType);
    
    if (userTokens.length === 0) {
      console.log(`ℹ️ No FCM tokens found for ${userType} ${userId}`);
      return { success: true, message: 'No tokens found' };
    }

    const tokens = userTokens.map(t => t.token);
    
    // Prepare notification payload
    const payload = {
      notification: {
        title,
        body: message,
        ...(imageUrl && { imageUrl })
      },
      data: {
        ...data,
        ...(clickAction && { click_action: clickAction }),
        userId,
        userType,
        timestamp: new Date().toISOString()
      },
      tokens
    };

    // Send notification
    const response = await messaging.sendEachForMulticast(payload);
    
    // Handle failed tokens
    const failedTokens: string[] = [];
    response.responses.forEach((resp, idx) => {
      if (!resp.success) {
        console.error(`❌ Failed to send to token ${tokens[idx]}:`, resp.error);
        failedTokens.push(tokens[idx]);
      }
    });

    // Clean up invalid tokens
    if (failedTokens.length > 0) {
      await cleanupInvalidTokens(failedTokens);
    }

    console.log(`✅ Push notification sent to ${userType} ${userId}. Success: ${response.successCount}/${tokens.length}`);
    
    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
      totalTokens: tokens.length
    };

  } catch (error) {
    console.error('❌ Error sending push notification to user:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

/**
 * Send push notification to multiple users
 */
export const sendPushNotificationToMultipleUsers = async (notification: MultipleUsersNotification) => {
  try {
    if (!messaging) {
      console.warn('⚠️ Firebase messaging not initialized. Skipping push notification.');
      return { success: false, error: 'Firebase not configured' };
    }

    const { userIds, userType, title, message, data, imageUrl, clickAction } = notification;

    // Get FCM tokens for all users
    const userTokens = await getMultipleUsersFcmTokens(userIds, userType);
    
    if (userTokens.length === 0) {
      console.log(`ℹ️ No FCM tokens found for ${userType} users: ${userIds.join(', ')}`);
      return { success: true, message: 'No tokens found' };
    }

    const tokens = userTokens.map(t => t.token);
    
    // Prepare notification payload
    const payload = {
      notification: {
        title,
        body: message,
        ...(imageUrl && { imageUrl })
      },
      data: {
        ...data,
        ...(clickAction && { click_action: clickAction }),
        userType,
        timestamp: new Date().toISOString()
      },
      tokens
    };

    // Send notification
    const response = await messaging.sendEachForMulticast(payload);
    
    // Handle failed tokens
    const failedTokens: string[] = [];
    response.responses.forEach((resp, idx) => {
      if (!resp.success) {
        console.error(`❌ Failed to send to token ${tokens[idx]}:`, resp.error);
        failedTokens.push(tokens[idx]);
      }
    });

    // Clean up invalid tokens
    if (failedTokens.length > 0) {
      await cleanupInvalidTokens(failedTokens);
    }

    console.log(`✅ Push notification sent to ${userIds.length} ${userType} users. Success: ${response.successCount}/${tokens.length}`);
    
    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
      totalTokens: tokens.length,
      totalUsers: userIds.length
    };

  } catch (error) {
    console.error('❌ Error sending push notification to multiple users:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

/**
 * Send push notification to all admin users
 */
export const sendPushNotificationToAllAdmins = async (notification: PushNotificationData) => {
  try {
    // Get all admin user IDs from your existing function
    const { getAdminUserIds } = await import('@/utils/notifications');
    const adminIds = await getAdminUserIds();
    
    if (adminIds.length === 0) {
      console.log('ℹ️ No admin users found');
      return { success: true, message: 'No admin users found' };
    }

    return await sendPushNotificationToMultipleUsers({
      ...notification,
      userIds: adminIds,
      userType: UserType.ADMIN
    });

  } catch (error) {
    console.error('❌ Error sending push notification to all admins:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};
